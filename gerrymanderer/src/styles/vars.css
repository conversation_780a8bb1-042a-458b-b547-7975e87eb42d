:root {
  --red: #dc143c;
  --blue: #0066cc;
  --purple: #814c9c;

  --animation-duration: 0.25s;

  --grid-cell-border-color: white;
  --grid-cell-border-image-slice: 5;
  --grid-cell-border-image-size: calc(
    var(--grid-cell-border-image-slice) * 1px
  );

  --grid-cell-border-image-top: linear-gradient(
    to bottom,
    var(--grid-cell-border-color) 0,
    var(--grid-cell-border-color) var(--grid-cell-border-image-size),
    transparent var(--grid-cell-border-image-size)
  );
  --grid-cell-border-image-right: linear-gradient(
    to left,
    var(--grid-cell-border-color) 0,
    var(--grid-cell-border-color) var(--grid-cell-border-image-size),
    transparent var(--grid-cell-border-image-size)
  );
  --grid-cell-border-image-bottom: linear-gradient(
    to top,
    var(--grid-cell-border-color) 0,
    var(--grid-cell-border-color) var(--grid-cell-border-image-size),
    transparent var(--grid-cell-border-image-size)
  );
  --grid-cell-border-image-left: linear-gradient(
    to right,
    var(--grid-cell-border-color) 0,
    var(--grid-cell-border-color) var(--grid-cell-border-image-size),
    transparent var(--grid-cell-border-image-size)
  );
}
