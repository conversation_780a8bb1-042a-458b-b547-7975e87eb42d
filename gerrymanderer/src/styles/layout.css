* {
  box-sizing: border-box;
  user-select: none;
}

::-moz-selection {
  color: inherit;
  background-color: inherit;
}

::selection {
  color: inherit;
  background-color: transparent;
}

html,
body {
  padding: 0;
  margin: 0;
  height: 100dvh;
}

body {
  background: linear-gradient(45deg, #e6e6fa 25%, #f0f8ff 0, #f0f8ff 50%, #e6e6fa 0, #e6e6fa 75%, #f0f8ff 0) 0% 0% / 3rem 3rem;
  padding: 1rem 0.5rem;
  max-width: 24rem;
  margin: 0 auto;
}

header {
  height: 6rem;
}

main {
  display: flex;
  align-items: center;
  justify-content: start;
  flex-direction: column;
  gap: 0.5rem;
  height: 32rem;
}

.board {
  display: flex;
  place-items: center;
  place-content: center;
  background: #bd9cceff;
  width: 20rem;
  aspect-ratio: 1;
  padding: 1rem;
}

.grid {
  position: relative;
  z-index: 0;
  width: 100%; 
  display: grid;
  margin: auto;
  place-items: center;
  grid-template-columns: repeat(var(--grid-size-x), 1fr);
  grid-template-rows: repeat(var(--grid-size-y), 1fr);
}

.flex-center {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  justify-content: center;
}

.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
  clip-path: inset(50%);
  white-space: nowrap;
}

.voters {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  width: calc(var(--count) * 4rem);
  pointer-events: none;
}

.illustration {
  position: relative;
  margin: 0.5rem auto 0;
  padding: 1rem;
  background: #bd9cceff;
}

.bubbles {
  display: flex;
  position: absolute;
  top: -0.5rem;
  left: 0;
  z-index: 10;
  width: 100%;
  flex-direction: row;
  justify-content: center;
  gap: 0.25rem;
  white-space: nowrap;
}
