.button {
  flex-shrink: 0;
  font-size: 1rem;
  line-height: 1;
  font-family: inherit;
  text-transform: uppercase;
  text-align: center;
  text-decoration: none;
  color: white;
  border-radius: 0;
  border: 0.25rem solid black;
  outline: none;
  background-color: var(--purple);
  box-shadow:
    inset 0.1rem 0.1rem 0 rgba(255, 255, 255, 0.3),
    inset -0.1rem -0.1rem 0 rgba(0, 0, 0, 0.3),
    0.1rem 0.1rem 0 black;
  padding: 0.5rem 0.75rem;
  cursor: pointer;
  z-index: 1;
  transform-origin: center;
  transition-duration: var(--animation-duration);
  transition-timing-function: ease;
  transition-property: opacity, scale, rotate;
  transition-behavior: allow-discrete;

  @starting-style {
    opacity: 0;
    scale: 0.95;
    rotate: 0deg;
  }
}

.button.disabled {
  pointer-events: none;
  background: grey;
  visibility: hidden;
}

@media (hover: hover) {
  .button:hover {
    scale: 1.05;
    rotate: 1deg;
  }
}

.button:active {
  scale: 0.95;
  rotate: -1deg;
}

.button:has(svg) {
  padding: 0.125rem 0.125rem;
}

.button svg {
  display: block;
  width: 2rem;
  height: 2rem;
  fill: white;
}
