.meter {
  display: flex;
  width: 8rem;
  height: 1.5rem;
  flex-grow: 1;
  background: black;
  border: 0.25rem solid black;
  padding: 0;
  margin: 0;
}

.open,
.red,
.blue {
  display: block;
  color: transparent;
  padding: 0;
  margin: 0;
  transition: width 0.25s ease;
}

.open {
  order: 2;
  background: black;
  height: 100%;
  flex-grow: 1;
  flex-shrink: 1;
  width: 100%;
}

.red {
  background: var(--red);
  height: 100%;
  order: 3;
}

.blue {
  background: var(--blue);
  height: 100%;
  order: 1;
}
